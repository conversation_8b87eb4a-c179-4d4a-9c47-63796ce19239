"""
Macro Recorder Module
Records keyboard and mouse events using pynput and saves them as JSON
"""

import json
import time
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from pynput import mouse, keyboard
from pynput.mouse import <PERSON><PERSON>, Listener as MouseListener
from pynput.keyboard import Key, Listener as KeyboardListener
import threading


class MacroRecorder:
    def __init__(self):
        self.is_recording = False
        self.events = []
        self.start_time = None
        self.mouse_listener = None
        self.keyboard_listener = None
        self.current_macro_name = None
        
        # Ensure macros directory exists
        os.makedirs("macros", exist_ok=True)
    
    def start_recording(self, macro_name: str) -> bool:
        """
        Start recording keyboard and mouse events
        
        Args:
            macro_name: Name for the macro being recorded
            
        Returns:
            bool: True if recording started successfully
        """
        if self.is_recording:
            print("Already recording a macro")
            return False
        
        try:
            self.current_macro_name = macro_name
            self.events = []
            self.start_time = time.time()
            self.is_recording = True
            
            # Start mouse listener
            self.mouse_listener = MouseListener(
                on_move=self._on_mouse_move,
                on_click=self._on_mouse_click,
                on_scroll=self._on_mouse_scroll
            )
            
            # Start keyboard listener
            self.keyboard_listener = KeyboardListener(
                on_press=self._on_key_press,
                on_release=self._on_key_release
            )
            
            self.mouse_listener.start()
            self.keyboard_listener.start()
            
            print(f"Started recording macro: {macro_name}")
            return True
            
        except Exception as e:
            print(f"Error starting macro recording: {str(e)}")
            self.is_recording = False
            return False
    
    def stop_recording(self) -> Optional[str]:
        """
        Stop recording and save the macro to a JSON file
        
        Returns:
            str: Path to the saved macro file, or None if failed
        """
        if not self.is_recording:
            print("No recording in progress")
            return None
        
        try:
            self.is_recording = False
            
            # Stop listeners
            if self.mouse_listener:
                self.mouse_listener.stop()
            if self.keyboard_listener:
                self.keyboard_listener.stop()
            
            # Save macro to file
            macro_file = self._save_macro()
            
            print(f"Stopped recording. Saved {len(self.events)} events to {macro_file}")
            return macro_file
            
        except Exception as e:
            print(f"Error stopping macro recording: {str(e)}")
            return None
    
    def _on_mouse_move(self, x: int, y: int):
        """Handle mouse move events"""
        if not self.is_recording:
            return
        
        # Only record significant mouse movements to reduce file size
        if len(self.events) == 0 or self._should_record_mouse_move(x, y):
            self._add_event({
                'type': 'mouse_move',
                'x': x,
                'y': y,
                'timestamp': time.time() - self.start_time
            })
    
    def _should_record_mouse_move(self, x: int, y: int) -> bool:
        """Determine if mouse move should be recorded (to reduce noise)"""
        if not self.events:
            return True
        
        # Get last mouse move event
        for event in reversed(self.events):
            if event['type'] == 'mouse_move':
                last_x, last_y = event['x'], event['y']
                # Only record if movement is significant (more than 5 pixels)
                distance = ((x - last_x) ** 2 + (y - last_y) ** 2) ** 0.5
                return distance > 5
            break
        
        return True
    
    def _on_mouse_click(self, x: int, y: int, button: Button, pressed: bool):
        """Handle mouse click events"""
        if not self.is_recording:
            return
        
        button_name = button.name if hasattr(button, 'name') else str(button)
        
        self._add_event({
            'type': 'mouse_click',
            'x': x,
            'y': y,
            'button': button_name,
            'pressed': pressed,
            'timestamp': time.time() - self.start_time
        })
    
    def _on_mouse_scroll(self, x: int, y: int, dx: int, dy: int):
        """Handle mouse scroll events"""
        if not self.is_recording:
            return
        
        self._add_event({
            'type': 'mouse_scroll',
            'x': x,
            'y': y,
            'dx': dx,
            'dy': dy,
            'timestamp': time.time() - self.start_time
        })
    
    def _on_key_press(self, key):
        """Handle key press events"""
        if not self.is_recording:
            return
        
        key_name = self._get_key_name(key)
        
        self._add_event({
            'type': 'key_press',
            'key': key_name,
            'timestamp': time.time() - self.start_time
        })
    
    def _on_key_release(self, key):
        """Handle key release events"""
        if not self.is_recording:
            return
        
        key_name = self._get_key_name(key)
        
        self._add_event({
            'type': 'key_release',
            'key': key_name,
            'timestamp': time.time() - self.start_time
        })
        
        # Stop recording if ESC key is pressed (emergency stop)
        if key == Key.esc:
            print("ESC pressed - stopping recording")
            threading.Thread(target=self.stop_recording, daemon=True).start()
    
    def _get_key_name(self, key) -> str:
        """Convert key object to string representation"""
        try:
            if hasattr(key, 'char') and key.char is not None:
                return key.char
            elif hasattr(key, 'name'):
                return key.name
            else:
                return str(key).replace('Key.', '')
        except AttributeError:
            return str(key)
    
    def _add_event(self, event: Dict[str, Any]):
        """Add an event to the recording"""
        if self.is_recording:
            self.events.append(event)
    
    def _save_macro(self) -> str:
        """Save recorded events to a JSON file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.current_macro_name}_{timestamp}.json"
        filepath = os.path.join("macros", filename)
        
        macro_data = {
            'name': self.current_macro_name,
            'created': datetime.now().isoformat(),
            'duration': time.time() - self.start_time if self.start_time else 0,
            'event_count': len(self.events),
            'events': self.events
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(macro_data, f, indent=2, ensure_ascii=False)
        
        return filepath
    
    def get_recorded_macros(self) -> List[Dict[str, Any]]:
        """Get list of all recorded macros"""
        macros = []
        
        if not os.path.exists("macros"):
            return macros
        
        for filename in os.listdir("macros"):
            if filename.endswith('.json'):
                filepath = os.path.join("macros", filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        macro_data = json.load(f)
                        macros.append({
                            'filename': filename,
                            'name': macro_data.get('name', 'Unknown'),
                            'created': macro_data.get('created', 'Unknown'),
                            'duration': macro_data.get('duration', 0),
                            'event_count': macro_data.get('event_count', 0)
                        })
                except Exception as e:
                    print(f"Error reading macro file {filename}: {str(e)}")
        
        return sorted(macros, key=lambda x: x['created'], reverse=True)
    
    def delete_macro(self, macro_name: str) -> bool:
        """
        Delete a recorded macro
        
        Args:
            macro_name: Name of the macro to delete
            
        Returns:
            bool: True if deleted successfully
        """
        try:
            macros = self.get_recorded_macros()
            for macro in macros:
                if macro['name'] == macro_name:
                    filepath = os.path.join("macros", macro['filename'])
                    os.remove(filepath)
                    print(f"Deleted macro: {macro_name}")
                    return True
            
            print(f"Macro not found: {macro_name}")
            return False
            
        except Exception as e:
            print(f"Error deleting macro {macro_name}: {str(e)}")
            return False
    
    def get_macro_details(self, macro_name: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a specific macro
        
        Args:
            macro_name: Name of the macro
            
        Returns:
            Dict with macro details or None if not found
        """
        try:
            macros = self.get_recorded_macros()
            for macro in macros:
                if macro['name'] == macro_name:
                    filepath = os.path.join("macros", macro['filename'])
                    with open(filepath, 'r', encoding='utf-8') as f:
                        return json.load(f)
            
            return None
            
        except Exception as e:
            print(f"Error getting macro details for {macro_name}: {str(e)}")
            return None
    
    def is_recording_active(self) -> bool:
        """Check if recording is currently active"""
        return self.is_recording
    
    def get_current_macro_name(self) -> Optional[str]:
        """Get the name of the currently recording macro"""
        return self.current_macro_name if self.is_recording else None
    
    def get_recording_duration(self) -> float:
        """Get the duration of the current recording in seconds"""
        if self.is_recording and self.start_time:
            return time.time() - self.start_time
        return 0.0
    
    def get_event_count(self) -> int:
        """Get the number of events recorded in the current session"""
        return len(self.events) if self.is_recording else 0
