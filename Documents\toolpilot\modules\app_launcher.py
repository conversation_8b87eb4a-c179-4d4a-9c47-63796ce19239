"""
Application Launcher Module
Handles launching desktop applications using subprocess
"""

import subprocess
import os
import platform
import psutil
from typing import Optional, Dict, List


class AppLauncher:
    def __init__(self):
        self.system = platform.system().lower()
        self.app_paths = self._get_common_app_paths()
        
    def _get_common_app_paths(self) -> Dict[str, str]:
        """Get common application paths for different operating systems"""
        if self.system == "windows":
            return {
                "notepad": "notepad.exe",
                "calculator": "calc.exe",
                "chrome": r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                "firefox": r"C:\Program Files\Mozilla Firefox\firefox.exe",
                "edge": "msedge.exe",
                "vscode": r"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe".format(os.getenv('USERNAME')),
                "cmd": "cmd.exe",
                "powershell": "powershell.exe",
                "explorer": "explorer.exe",
                "paint": "mspaint.exe",
                "wordpad": "wordpad.exe",
                "task_manager": "taskmgr.exe",
                "control_panel": "control.exe",
                "registry": "regedit.exe"
            }
        elif self.system == "darwin":  # macOS
            return {
                "chrome": "/Applications/Google Chrome.app",
                "firefox": "/Applications/Firefox.app",
                "safari": "/Applications/Safari.app",
                "vscode": "/Applications/Visual Studio Code.app",
                "terminal": "/Applications/Utilities/Terminal.app",
                "finder": "/System/Library/CoreServices/Finder.app",
                "calculator": "/Applications/Calculator.app",
                "textedit": "/Applications/TextEdit.app"
            }
        else:  # Linux
            return {
                "chrome": "google-chrome",
                "firefox": "firefox",
                "vscode": "code",
                "terminal": "gnome-terminal",
                "calculator": "gnome-calculator",
                "gedit": "gedit",
                "nautilus": "nautilus"
            }
    
    def launch_app(self, app_name: str, args: Optional[List[str]] = None) -> bool:
        """
        Launch an application by name
        
        Args:
            app_name: Name of the application to launch
            args: Optional arguments to pass to the application
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            app_name_lower = app_name.lower()
            
            # Check if it's a known application
            if app_name_lower in self.app_paths:
                app_path = self.app_paths[app_name_lower]
                return self._launch_by_path(app_path, args)
            
            # Try to launch directly (might be in PATH)
            return self._launch_by_name(app_name, args)
            
        except Exception as e:
            print(f"Error launching {app_name}: {str(e)}")
            return False
    
    def _launch_by_path(self, app_path: str, args: Optional[List[str]] = None) -> bool:
        """Launch application by full path"""
        try:
            if self.system == "windows":
                # Check if file exists
                if not os.path.exists(app_path):
                    # Try alternative locations for common apps
                    alt_path = self._find_alternative_path(app_path)
                    if alt_path:
                        app_path = alt_path
                    else:
                        return False
                
                cmd = [app_path]
                if args:
                    cmd.extend(args)
                    
                subprocess.Popen(cmd, shell=True)
                return True
                
            elif self.system == "darwin":  # macOS
                cmd = ["open", app_path]
                if args:
                    cmd.extend(["--args"] + args)
                subprocess.Popen(cmd)
                return True
                
            else:  # Linux
                cmd = [app_path]
                if args:
                    cmd.extend(args)
                subprocess.Popen(cmd)
                return True
                
        except Exception as e:
            print(f"Error launching by path {app_path}: {str(e)}")
            return False
    
    def _launch_by_name(self, app_name: str, args: Optional[List[str]] = None) -> bool:
        """Launch application by name (assuming it's in PATH)"""
        try:
            cmd = [app_name]
            if args:
                cmd.extend(args)
                
            subprocess.Popen(cmd, shell=True if self.system == "windows" else False)
            return True
            
        except Exception as e:
            print(f"Error launching by name {app_name}: {str(e)}")
            return False
    
    def _find_alternative_path(self, original_path: str) -> Optional[str]:
        """Find alternative paths for common applications"""
        if self.system != "windows":
            return None
            
        app_name = os.path.basename(original_path).lower()
        
        # Common alternative locations
        alternative_locations = [
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe",
            r"C:\Program Files\Mozilla Firefox\firefox.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe".format(os.getenv('USERNAME')),
            r"C:\Program Files\Microsoft VS Code\Code.exe"
        ]
        
        for alt_path in alternative_locations:
            if os.path.exists(alt_path) and app_name in alt_path.lower():
                return alt_path
                
        return None
    
    def open_url(self, url: str, browser: Optional[str] = None) -> bool:
        """
        Open a URL in the default or specified browser
        
        Args:
            url: URL to open
            browser: Optional browser name (chrome, firefox, edge, etc.)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if browser:
                browser_lower = browser.lower()
                if browser_lower in self.app_paths:
                    return self._launch_by_path(self.app_paths[browser_lower], [url])
            
            # Use default browser
            if self.system == "windows":
                subprocess.Popen(["start", url], shell=True)
            elif self.system == "darwin":
                subprocess.Popen(["open", url])
            else:
                subprocess.Popen(["xdg-open", url])
                
            return True
            
        except Exception as e:
            print(f"Error opening URL {url}: {str(e)}")
            return False
    
    def open_file(self, file_path: str, app: Optional[str] = None) -> bool:
        """
        Open a file with the default or specified application
        
        Args:
            file_path: Path to the file to open
            app: Optional application name to open the file with
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not os.path.exists(file_path):
                print(f"File not found: {file_path}")
                return False
            
            if app:
                app_lower = app.lower()
                if app_lower in self.app_paths:
                    return self._launch_by_path(self.app_paths[app_lower], [file_path])
                else:
                    return self._launch_by_name(app, [file_path])
            
            # Use default application
            if self.system == "windows":
                subprocess.Popen(["start", file_path], shell=True)
            elif self.system == "darwin":
                subprocess.Popen(["open", file_path])
            else:
                subprocess.Popen(["xdg-open", file_path])
                
            return True
            
        except Exception as e:
            print(f"Error opening file {file_path}: {str(e)}")
            return False
    
    def is_app_running(self, app_name: str) -> bool:
        """
        Check if an application is currently running
        
        Args:
            app_name: Name of the application to check
            
        Returns:
            bool: True if running, False otherwise
        """
        try:
            app_name_lower = app_name.lower()
            
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    process_name = proc.info['name'].lower()
                    if app_name_lower in process_name or process_name in app_name_lower:
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            return False
            
        except Exception as e:
            print(f"Error checking if {app_name} is running: {str(e)}")
            return False
    
    def close_app(self, app_name: str) -> bool:
        """
        Close a running application
        
        Args:
            app_name: Name of the application to close
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            app_name_lower = app_name.lower()
            closed_any = False
            
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    process_name = proc.info['name'].lower()
                    if app_name_lower in process_name or process_name in app_name_lower:
                        proc.terminate()
                        closed_any = True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            return closed_any
            
        except Exception as e:
            print(f"Error closing {app_name}: {str(e)}")
            return False
    
    def get_available_apps(self) -> List[str]:
        """Get list of available applications"""
        return list(self.app_paths.keys())
    
    def add_custom_app(self, name: str, path: str) -> None:
        """Add a custom application path"""
        self.app_paths[name.lower()] = path
