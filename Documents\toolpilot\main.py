#!/usr/bin/env python3
"""
Universal Automation Agent - Main Application
A desktop automation tool that can execute commands via keyboard/mouse,
record/replay macros, and accept voice commands.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import json
import os
from datetime import datetime

from modules.app_launcher import AppLauncher
from modules.automation import AutomationEngine
from modules.macro_recorder import MacroRecorder
from modules.macro_player import MacroPlayer
from modules.voice_recognition import VoiceRecognition
from modules.command_parser import CommandParser


class UniversalAutomationAgent:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Universal Automation Agent")
        self.root.geometry("800x600")
        
        # Initialize modules
        self.app_launcher = AppLauncher()
        self.automation = AutomationEngine()
        self.macro_recorder = MacroRecorder()
        self.macro_player = MacroPlayer()
        self.voice_recognition = VoiceRecognition()
        self.command_parser = CommandParser(self)
        
        # State variables
        self.is_recording = False
        self.is_listening = False
        
        self.setup_ui()
        self.setup_directories()
        
    def setup_directories(self):
        """Create necessary directories for the application"""
        os.makedirs("macros", exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        
    def setup_ui(self):
        """Setup the main user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Universal Automation Agent", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Command input section
        ttk.Label(main_frame, text="Command:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.command_entry = ttk.Entry(main_frame, width=50)
        self.command_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        self.command_entry.bind('<Return>', self.execute_command)
        
        execute_btn = ttk.Button(main_frame, text="Execute", command=self.execute_command)
        execute_btn.grid(row=1, column=2, pady=5, padx=(5, 0))
        
        # Voice control section
        voice_frame = ttk.LabelFrame(main_frame, text="Voice Control", padding="5")
        voice_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        voice_frame.columnconfigure(1, weight=1)
        
        self.voice_btn = ttk.Button(voice_frame, text="Start Listening", 
                                   command=self.toggle_voice_listening)
        self.voice_btn.grid(row=0, column=0, pady=5)
        
        self.voice_status = ttk.Label(voice_frame, text="Voice recognition ready")
        self.voice_status.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # Macro control section
        macro_frame = ttk.LabelFrame(main_frame, text="Macro Control", padding="5")
        macro_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        self.record_btn = ttk.Button(macro_frame, text="Start Recording", 
                                    command=self.toggle_macro_recording)
        self.record_btn.grid(row=0, column=0, pady=5)
        
        ttk.Label(macro_frame, text="Macro Name:").grid(row=0, column=1, padx=(20, 5))
        self.macro_name_entry = ttk.Entry(macro_frame, width=20)
        self.macro_name_entry.grid(row=0, column=2, pady=5)
        
        self.play_btn = ttk.Button(macro_frame, text="Play Macro", 
                                  command=self.play_macro)
        self.play_btn.grid(row=0, column=3, pady=5, padx=(10, 0))
        
        # Log output
        ttk.Label(main_frame, text="Output Log:").grid(row=4, column=0, sticky=tk.W, pady=(20, 5))
        self.log_text = scrolledtext.ScrolledText(main_frame, height=15, width=80)
        self.log_text.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # Configure row weight for log expansion
        main_frame.rowconfigure(5, weight=1)
        
    def log_message(self, message):
        """Add a message to the log output"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        
    def execute_command(self, event=None):
        """Execute a text command"""
        command = self.command_entry.get().strip()
        if not command:
            return
            
        self.log_message(f"Executing command: {command}")
        self.command_entry.delete(0, tk.END)
        
        # Run command in separate thread to avoid blocking UI
        threading.Thread(target=self._execute_command_thread, args=(command,), daemon=True).start()
        
    def _execute_command_thread(self, command):
        """Execute command in a separate thread"""
        try:
            result = self.command_parser.parse_and_execute(command)
            if result:
                self.log_message(f"Command executed successfully: {result}")
            else:
                self.log_message("Command execution failed - falling back to macro recording")
                self.fallback_to_macro_recording(command)
        except Exception as e:
            self.log_message(f"Error executing command: {str(e)}")
            
    def fallback_to_macro_recording(self, command):
        """Fallback to macro recording when automation fails"""
        self.log_message("Please perform the action manually - recording will start in 3 seconds...")
        # Implementation will be added in macro recorder module
        
    def toggle_voice_listening(self):
        """Toggle voice recognition on/off"""
        if not self.is_listening:
            self.is_listening = True
            self.voice_btn.config(text="Stop Listening")
            self.voice_status.config(text="Listening for commands...")
            threading.Thread(target=self._voice_listening_thread, daemon=True).start()
        else:
            self.is_listening = False
            self.voice_btn.config(text="Start Listening")
            self.voice_status.config(text="Voice recognition stopped")
            
    def _voice_listening_thread(self):
        """Voice listening thread"""
        while self.is_listening:
            try:
                command = self.voice_recognition.listen_for_command()
                if command:
                    self.root.after(0, lambda: self.command_entry.insert(0, command))
                    self.log_message(f"Voice command received: {command}")
            except Exception as e:
                self.log_message(f"Voice recognition error: {str(e)}")
                
    def toggle_macro_recording(self):
        """Toggle macro recording on/off"""
        if not self.is_recording:
            macro_name = self.macro_name_entry.get().strip()
            if not macro_name:
                messagebox.showwarning("Warning", "Please enter a macro name")
                return
                
            self.is_recording = True
            self.record_btn.config(text="Stop Recording")
            self.log_message(f"Started recording macro: {macro_name}")
            self.macro_recorder.start_recording(macro_name)
        else:
            self.is_recording = False
            self.record_btn.config(text="Start Recording")
            macro_file = self.macro_recorder.stop_recording()
            self.log_message(f"Stopped recording. Macro saved to: {macro_file}")
            
    def play_macro(self):
        """Play a recorded macro"""
        macro_name = self.macro_name_entry.get().strip()
        if not macro_name:
            messagebox.showwarning("Warning", "Please enter a macro name to play")
            return
            
        self.log_message(f"Playing macro: {macro_name}")
        threading.Thread(target=self._play_macro_thread, args=(macro_name,), daemon=True).start()
        
    def _play_macro_thread(self, macro_name):
        """Play macro in separate thread"""
        try:
            result = self.macro_player.play_macro(macro_name)
            if result:
                self.log_message(f"Macro '{macro_name}' played successfully")
            else:
                self.log_message(f"Failed to play macro '{macro_name}'")
        except Exception as e:
            self.log_message(f"Error playing macro: {str(e)}")
            
    def run(self):
        """Start the application"""
        self.log_message("Universal Automation Agent started")
        self.log_message("Ready to accept commands!")
        self.root.mainloop()


if __name__ == "__main__":
    app = UniversalAutomationAgent()
    app.run()
