"""
Automation Engine Module
Handles keyboard and mouse automation using pyautogui
"""

import pyautogui
import time
import cv2
import numpy as np
from typing import Tuple, Optional, List, Union
import os
from PIL import Image


class AutomationEngine:
    def __init__(self):
        # Configure pyautogui settings
        pyautogui.FAILSAFE = True  # Move mouse to top-left corner to abort
        pyautogui.PAUSE = 0.1  # Small pause between actions
        
        # Get screen dimensions
        self.screen_width, self.screen_height = pyautogui.size()
        
    def type_text(self, text: str, interval: float = 0.05) -> bool:
        """
        Type text with specified interval between characters
        
        Args:
            text: Text to type
            interval: Delay between characters (seconds)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            pyautogui.write(text, interval=interval)
            return True
        except Exception as e:
            print(f"Error typing text: {str(e)}")
            return False
    
    def press_key(self, key: str, presses: int = 1, interval: float = 0.1) -> bool:
        """
        Press a key or key combination
        
        Args:
            key: Key to press (e.g., 'enter', 'ctrl+c', 'alt+tab')
            presses: Number of times to press the key
            interval: Delay between presses
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if '+' in key:
                # Handle key combinations
                keys = key.split('+')
                pyautogui.hotkey(*keys)
            else:
                # Handle single key
                pyautogui.press(key, presses=presses, interval=interval)
            return True
        except Exception as e:
            print(f"Error pressing key {key}: {str(e)}")
            return False
    
    def click(self, x: int, y: int, button: str = 'left', clicks: int = 1, 
              interval: float = 0.1) -> bool:
        """
        Click at specified coordinates
        
        Args:
            x: X coordinate
            y: Y coordinate
            button: Mouse button ('left', 'right', 'middle')
            clicks: Number of clicks
            interval: Delay between clicks
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            pyautogui.click(x, y, clicks=clicks, interval=interval, button=button)
            return True
        except Exception as e:
            print(f"Error clicking at ({x}, {y}): {str(e)}")
            return False
    
    def double_click(self, x: int, y: int) -> bool:
        """Double click at specified coordinates"""
        return self.click(x, y, clicks=2)
    
    def right_click(self, x: int, y: int) -> bool:
        """Right click at specified coordinates"""
        return self.click(x, y, button='right')
    
    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, 
             duration: float = 1.0, button: str = 'left') -> bool:
        """
        Drag from start coordinates to end coordinates
        
        Args:
            start_x: Starting X coordinate
            start_y: Starting Y coordinate
            end_x: Ending X coordinate
            end_y: Ending Y coordinate
            duration: Duration of drag in seconds
            button: Mouse button to use for dragging
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration, 
                          button=button, startX=start_x, startY=start_y)
            return True
        except Exception as e:
            print(f"Error dragging from ({start_x}, {start_y}) to ({end_x}, {end_y}): {str(e)}")
            return False
    
    def scroll(self, clicks: int, x: Optional[int] = None, y: Optional[int] = None) -> bool:
        """
        Scroll at specified location or current mouse position
        
        Args:
            clicks: Number of scroll clicks (positive for up, negative for down)
            x: X coordinate (optional)
            y: Y coordinate (optional)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if x is not None and y is not None:
                pyautogui.scroll(clicks, x=x, y=y)
            else:
                pyautogui.scroll(clicks)
            return True
        except Exception as e:
            print(f"Error scrolling: {str(e)}")
            return False
    
    def move_mouse(self, x: int, y: int, duration: float = 0.5) -> bool:
        """
        Move mouse to specified coordinates
        
        Args:
            x: X coordinate
            y: Y coordinate
            duration: Duration of movement in seconds
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            pyautogui.moveTo(x, y, duration=duration)
            return True
        except Exception as e:
            print(f"Error moving mouse to ({x}, {y}): {str(e)}")
            return False
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """Get current mouse position"""
        return pyautogui.position()
    
    def take_screenshot(self, region: Optional[Tuple[int, int, int, int]] = None) -> Optional[Image.Image]:
        """
        Take a screenshot
        
        Args:
            region: Optional region tuple (left, top, width, height)
            
        Returns:
            PIL Image or None if failed
        """
        try:
            if region:
                return pyautogui.screenshot(region=region)
            else:
                return pyautogui.screenshot()
        except Exception as e:
            print(f"Error taking screenshot: {str(e)}")
            return None
    
    def find_image_on_screen(self, image_path: str, confidence: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Find an image on the screen and return its center coordinates
        
        Args:
            image_path: Path to the image file to find
            confidence: Confidence level for image matching (0.0 to 1.0)
            
        Returns:
            Tuple of (x, y) coordinates or None if not found
        """
        try:
            if not os.path.exists(image_path):
                print(f"Image file not found: {image_path}")
                return None
                
            location = pyautogui.locateOnScreen(image_path, confidence=confidence)
            if location:
                center = pyautogui.center(location)
                return (center.x, center.y)
            return None
        except Exception as e:
            print(f"Error finding image {image_path}: {str(e)}")
            return None
    
    def click_image(self, image_path: str, confidence: float = 0.8, 
                   button: str = 'left', clicks: int = 1) -> bool:
        """
        Find and click on an image
        
        Args:
            image_path: Path to the image file to find and click
            confidence: Confidence level for image matching
            button: Mouse button to use
            clicks: Number of clicks
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            location = self.find_image_on_screen(image_path, confidence)
            if location:
                return self.click(location[0], location[1], button=button, clicks=clicks)
            else:
                print(f"Image not found on screen: {image_path}")
                return False
        except Exception as e:
            print(f"Error clicking image {image_path}: {str(e)}")
            return False
    
    def wait_for_image(self, image_path: str, timeout: float = 10.0, 
                      confidence: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Wait for an image to appear on screen
        
        Args:
            image_path: Path to the image file to wait for
            timeout: Maximum time to wait in seconds
            confidence: Confidence level for image matching
            
        Returns:
            Tuple of (x, y) coordinates or None if timeout
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            location = self.find_image_on_screen(image_path, confidence)
            if location:
                return location
            time.sleep(0.5)
        
        print(f"Timeout waiting for image: {image_path}")
        return None
    
    def get_pixel_color(self, x: int, y: int) -> Tuple[int, int, int]:
        """
        Get the RGB color of a pixel at specified coordinates
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            Tuple of (R, G, B) values
        """
        try:
            return pyautogui.pixel(x, y)
        except Exception as e:
            print(f"Error getting pixel color at ({x}, {y}): {str(e)}")
            return (0, 0, 0)
    
    def wait_for_color(self, x: int, y: int, target_color: Tuple[int, int, int], 
                      timeout: float = 10.0, tolerance: int = 10) -> bool:
        """
        Wait for a pixel to change to a specific color
        
        Args:
            x: X coordinate
            y: Y coordinate
            target_color: Target RGB color tuple
            timeout: Maximum time to wait in seconds
            tolerance: Color tolerance (0-255)
            
        Returns:
            bool: True if color matched within timeout, False otherwise
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            current_color = self.get_pixel_color(x, y)
            
            # Check if colors match within tolerance
            if all(abs(current_color[i] - target_color[i]) <= tolerance for i in range(3)):
                return True
                
            time.sleep(0.1)
        
        return False
    
    def select_all(self) -> bool:
        """Select all text/content (Ctrl+A)"""
        return self.press_key('ctrl+a')
    
    def copy(self) -> bool:
        """Copy selected content (Ctrl+C)"""
        return self.press_key('ctrl+c')
    
    def paste(self) -> bool:
        """Paste clipboard content (Ctrl+V)"""
        return self.press_key('ctrl+v')
    
    def cut(self) -> bool:
        """Cut selected content (Ctrl+X)"""
        return self.press_key('ctrl+x')
    
    def undo(self) -> bool:
        """Undo last action (Ctrl+Z)"""
        return self.press_key('ctrl+z')
    
    def redo(self) -> bool:
        """Redo last undone action (Ctrl+Y)"""
        return self.press_key('ctrl+y')
    
    def save(self) -> bool:
        """Save current document (Ctrl+S)"""
        return self.press_key('ctrl+s')
    
    def open_file_dialog(self) -> bool:
        """Open file dialog (Ctrl+O)"""
        return self.press_key('ctrl+o')
    
    def new_document(self) -> bool:
        """Create new document (Ctrl+N)"""
        return self.press_key('ctrl+n')
    
    def alt_tab(self) -> bool:
        """Switch between applications (Alt+Tab)"""
        return self.press_key('alt+tab')
    
    def minimize_window(self) -> bool:
        """Minimize current window (Win+Down or Cmd+M)"""
        import platform
        if platform.system().lower() == "windows":
            return self.press_key('win+down')
        else:
            return self.press_key('cmd+m')
    
    def maximize_window(self) -> bool:
        """Maximize current window (Win+Up or Cmd+Ctrl+F)"""
        import platform
        if platform.system().lower() == "windows":
            return self.press_key('win+up')
        else:
            return self.press_key('cmd+ctrl+f')
    
    def close_window(self) -> bool:
        """Close current window (Alt+F4 or Cmd+W)"""
        import platform
        if platform.system().lower() == "windows":
            return self.press_key('alt+f4')
        else:
            return self.press_key('cmd+w')
